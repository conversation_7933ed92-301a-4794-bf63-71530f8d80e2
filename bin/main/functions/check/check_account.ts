import { execSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, ROOTDIR, SRC_ZONE_ID } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 指定したアカウントが指定したバリデータに存在するか確認する
 * @param accountId アカウントID
 * @param validatorId バリデータID
 */
export async function checkAccount(accountId: string, validatorId: string) {
  try {
    // AWS_PROFILEの設定
    const profile = loadAwsProf('mainFin')

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile, SRC_ZONE_ID)

    process.chdir(ROOTDIR)

    const output = execSync(
      `npx hardhat getAccountAll --network mainFin --account-id "${accountId}" --valid-id "${validatorId}"`,
      { encoding: 'utf-8' },
    )

    console.log('指定したアカウントが登録されていることを確認します。')

    console.log(output)

    if (output.includes('active')) {
      console.log('OK：指定したアカウントが登録されていることを確認しました。')
    } else {
      console.log('NG：指定したアカウントが登録されていません。')
    }
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const accountId = process.argv[2]
  const validatorId = process.argv[3]
  if (!accountId) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [ACCOUNT_ID] [VARIDATOR_ID]`)
    process.exit(1)
  }

  checkAccount(accountId, validatorId).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
