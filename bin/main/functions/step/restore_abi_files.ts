import { spawnSync } from 'child_process'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, menu, choice, ROOTDIR } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * バックアップ済みのデプロイ環境ファイルをローカルに復元する
 * @param network 環境名
 */
export async function restoreABIfiles(network: string) {
  try {
    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    const { BACKUP, ZONE_ID } = process.env
    if (!BACKUP || !ZONE_ID) {
      message('err', 'Environment variables BACKUP and ZONE_ID must be set.')
      process.exit(1)
    }

    const s3Prefix = `s3://${BACKUP}/${ZONE_ID}/contract/${network}/`
    message('info', 'Fetching list of backed up directories from S3...')

    // aws s3 ls
    const list = spawnSync('aws', ['s3', 'ls', s3Prefix], { encoding: 'utf-8' })
    if (list.status !== 0) {
      message('err', `Failed to list directories: ${list.stderr}`)
      process.exit(1)
    }

    const directories = list.stdout
      .split('\n')
      .map((line) => line.trim().split(/\s+/)[1])
      .filter((dir) => dir && dir.endsWith('/'))

    if (directories.length === 0) {
      message('err', 'No backups found.')
      process.exit(1)
    }

    // バックアップディレクトリ選択
    const choiceDir = await menu('Select a backup directory to restore:', directories)
    if (!choiceDir) {
      message('err', 'Invalid selection. Please try again.')
      process.exit(1)
    }

    await choice(`Are you sure you want to restore from "${s3Prefix}${choiceDir}" to "${ROOTDIR}"?`)

    message('info', `Restoring backup from "${s3Prefix}${choiceDir}" to "${ROOTDIR}"...`)

    // S3からローカルにファイルコピー
    const copy = spawnSync('aws', ['s3', 'cp', `${s3Prefix}${choiceDir}`, ROOTDIR, '--recursive'], {
      encoding: 'utf-8',
      stdio: 'inherit',
    })

    if (copy.status !== 0) {
      message('err', `Failed to restore backup: ${copy.stderr}`)
      process.exit(1)
    }

    message('success', 'Backup restoration completed.')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  restoreABIfiles(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
