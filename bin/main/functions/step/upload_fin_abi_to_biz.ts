import { spawnSync } from 'child_process'
import fs from 'fs'
import path from 'path'
import { loadAwsProf } from '../../../common/load_aws_prof'
import { loadEnvVars } from '../../../common/load_env_vars'
import { message, ROOTDIR, SRC_ZONE_ID } from '../../../common/utils'

// スクリプト名の取得
const scriptName = path.basename(__filename)

/**
 * 他コンポーネントがBizZoneで利用するABI.JSONをFinZoneからコピーしS3にアップロードする
 * @param network 環境名
 */
export async function uploadABIToBiz(network: string) {
  try {
    // ローカル環境向けのリリースの場合、処理をスキップする
    if (network.includes('local')) {
      message('info', `Skipping ${scriptName} for local environments.`)
      process.exit(0)
    }

    // AWS_PROFILEの設定
    const profile = loadAwsProf(network)

    // 環境変数の読み出しとエクスポート
    loadEnvVars(profile)

    const targetZoneId = process.env.ZONE_ID
    if (!targetZoneId) {
      message('err', 'ZONE_ID environment variable is not set.')
      process.exit(1)
    }

    // 対象のZONEIDが3000(Fin)だった場合処理をスキップする
    if (targetZoneId == SRC_ZONE_ID) {
      message('info', `Skipping ${scriptName} as ZONE_ID is 3000.`)
      process.exit(0)
    }

    // 必要な環境変数のチェック
    const backupS3Fin = process.env.BACKUP_S3_FIN
    const awsProfileFin = process.env.AWS_PROFILE_FIN
    const backupS3 = process.env.BACKUP_S3

    if (!backupS3Fin || !awsProfileFin) {
      message('err', 'Environment variables BACKUP_S3_FIN and AWS_PROFILE_FIN must be set.')
    }

    // contract/3000フォルダが指定したS3に存在するか確認
    const lsResult = spawnSync('aws', ['s3', 'ls', `s3://${backupS3Fin}/3000/`, '--profile', `${awsProfileFin}`], {
      stdio: 'ignore',
    })
    if (lsResult.status !== 0) {
      message(
        'err',
        `The folder s3://${backupS3Fin}/3000/ does not exist. Please execute the FinZone deployment first.`,
      )
      process.exit(1)
    }

    // コピー対象ファイル
    const finFiles = ['Issuer.json', 'FinancialCheck.json']

    // tmp ディレクトリ準備
    const tmpDir = path.join(ROOTDIR, 'tmp')
    if (!fs.existsSync(tmpDir)) {
      fs.mkdirSync(tmpDir, { recursive: true })
    }

    // ファイルコピー処理
    for (const file of finFiles) {
      message('info', `Copying ${file} from ${backupS3Fin} to ${backupS3}...`)

      // FinZoneからABIファイルをダウンロード
      const download = spawnSync(
        'aws',
        ['s3', 'cp', `s3://${backupS3Fin}/3000/${file}`, path.join(tmpDir, file), '--profile', `${awsProfileFin}`],
        { stdio: 'inherit' },
      )

      if (download.status !== 0) {
        message('err', `Failed to download ${file} from ${backupS3Fin}.`)
        process.exit(1)
      }

      // ダウンロードしたファイルをBizZoneにアップロード
      const upload = spawnSync(
        'aws',
        ['s3', 'cp', path.join(tmpDir, file), `s3://${backupS3}/${targetZoneId}/${file}`],
        { stdio: 'inherit' },
      )

      if (upload.status !== 0) {
        message('err', `Failed to upload ${file} to ${backupS3}.`)
        process.exit(1)
      }
    }

    // tmpディレクトリ削除
    fs.rmSync(tmpDir, { recursive: true, force: true })

    // アップロード済みファイル一覧を表示
    spawnSync('aws', ['s3', 'ls', `s3://${backupS3}/${targetZoneId}/`], { stdio: 'inherit' })

    message('success', 'All files copied successfully.')
  } catch (err) {
    message('err', `An unexpected error occurred: ${(err as Error).message}`)
    process.exit(1)
  }
}

// 直接実行用
if (require.main === module) {
  // 引数チェック
  const network = process.argv[2]
  if (!network) {
    message('err', 'Please specify the NETWORK as the first argument.')
    console.log(`npx ts-node bin/main/functions/step/${scriptName} [NETWORK name]`)
    process.exit(1)
  }

  uploadABIToBiz(network).catch((err) => {
    console.error('[ERROR]', err)
    process.exit(1)
  })
}
