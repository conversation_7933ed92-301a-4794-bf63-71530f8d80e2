import * as fs from 'fs'
import * as path from 'path'
import readline from 'readline'
import { clean } from '../common/clean'
import { message, ROOTDIR } from '../common/utils'
import { backUpABIfiles } from './functions/step/backup_abi_files'
import { deployedConfirmation } from './functions/step/deployed_confirmation'
import { disconnectPortForward } from './functions/step/disconnect_port_forward'
import { generateEnv } from './functions/step/generate_env'
import { generateKmsKey } from './functions/step/generate_kms_key'
import { migrateIbc } from './functions/step/migrate_ibc'
import { migrateMain } from './functions/step/migrate_main'
import { portForward } from './functions/step/port_forward'
import { registerEscrowAccount } from './functions/step/register_escrow_account'
import { restoreABIfiles } from './functions/step/restore_abi_files'
import { runSetIBCApp } from './functions/step/set_ibc_app'
import { uploadABIfiles } from './functions/step/upload_abi_files'
import { uploadABIToBiz } from './functions/step/upload_fin_abi_to_biz'

// 後続の処理の誤反応を防ぐためコマンドライン引数をリセット
process.argv = [process.argv[0], process.argv[1]]

describe('コントラクトリリース', function () {
  this.timeout(0)

  let network: string
  let deployTarget: string
  let numberOfBizZone: number

  it('KMSキー作成', async function () {
    network = await generateKmsKey()
  })

  it('デプロイ対象選択', async function () {
    deployTarget = await selectDeployTarget()
  })

  it('ZONE_IDが3000だった場合、登録するビジネスゾーンの数を入力', async function () {
    numberOfBizZone = await registerBizZoneNumber()
  })

  it('デプロイ前のキャッシュクリア実行', function () {
    clean(network)
  })

  it('デプロイ前にバックアップからローカルファイルのリストアを行うか選択', async function () {
    await selectRestore(network)
  })

  it('ポートフォワード接続する', async function () {
    await portForward(network)
  })

  it('コントラクトデプロイ実行', async function () {
    switch (deployTarget) {
      case '1':
        //contract-mainのみをデプロイ実行する
        await migrateMain(network)
        await deployedConfirmation(network, 'main')
        break
      case '2':
        //contract-ibcのみをデプロイ実行する
        await migrateIbc(network)
        await deployedConfirmation(network, 'ibc')
        await registerEscrowAccount(network, 1)
        break
      case '3':
        //contract-main、contract-ibcをデプロイ実行する
        await migrateMain(network)
        await deployedConfirmation(network, 'main')

        // contract-ibcのデプロイ準備
        await prepareIbcDeployment(ROOTDIR, network)

        await migrateIbc(network)
        await runSetIBCApp(network)
        await deployedConfirmation(network, 'ibc')

        await registerEscrowAccount(network, numberOfBizZone)
        break
    }
  })

  it('ポートフォワードを切断する', async function () {
    await disconnectPortForward(network)
  })

  it('コントラクトのバックアップファイルをアップロードする', async function () {
    await backUpABIfiles(network)
  })

  it('ABIファイルをバックアップする', async function () {
    await uploadABIfiles(network)
  })

  it('FINのABIファイルの一部をBIZにアップロードする', async function () {
    if (deployTarget == '1' || deployTarget == '3') {
      await uploadABIToBiz(network)
    }
  })

  after(function () {
    message('success', 'Deployment process completed successfully.')
  })
})

/**
 * 標準入力からユーザーの入力値を受け取る
 * @param promptText ターミナルに表示する文字列
 * @returns ユーザーの入力値
 */
function readInput(promptText?: string): Promise<string> {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    })

    rl.question(promptText ?? '', (answer) => {
      rl.close()
      resolve(answer.trim())
    })
  })
}

/**
 * ユーザーにデプロイ対象を選択させる
 * @returns ユーザーが選んだデプロイ対象(1:main,2:ibc,3:mainとibc両方)
 */
async function selectDeployTarget(): Promise<string> {
  let roopFlg = true
  let deployTarget

  while (roopFlg) {
    message('info', 'please enter the deployment target? (1:main,2:ibc,3:both)')
    deployTarget = await readInput('> ')
    switch (deployTarget) {
      case '1':
        message('info', 'deployment target contract-main')
        roopFlg = false
        break
      case '2':
        message('info', 'deployment target contract-ibc')
        roopFlg = false
        break
      case '3':
        message('info', 'deployment target contract-main, contract-ibc')
        roopFlg = false
        break
      default:
        break
    }
  }

  return deployTarget
}

/**
 * ユーザーにBizZoneの数を入力させる
 * @returns ユーザーが入力した数値
 */
export async function registerBizZoneNumber(): Promise<number> {
  const zoneId = process.env.ZONE_ID
  let numberOfBizZone

  if (zoneId === '3000') {
    message('info', 'How many biz zone do you want to register?')

    const input = await readInput('> ')
    numberOfBizZone = Number(input)

    // 入力値のバリデーション
    if (isNaN(numberOfBizZone) || numberOfBizZone < 1 || numberOfBizZone > 999) {
      message('err', 'Please enter a number between 1 and 999')
      process.exit(1)
    }

    // 登録範囲を表示
    message('info', `Registering BizZone from 3001 to ${3000 + numberOfBizZone}`)

    // 環境変数にセット
    process.env.NUMBER_OF_BIZZONE = numberOfBizZone.toString()
  }

  return numberOfBizZone
}

/**
 * ユーザーにリストアさせるか選択させる
 * @param network 環境名
 */
async function selectRestore(network: string) {
  let roopFlg = true
  let answer

  while (roopFlg) {
    message('q', 'Do you want to restore from backup before deployment? (y/N): ')
    answer = await readInput('')
    switch (answer) {
      case 'y':
      case 'Y':
        await restoreABIfiles(network)
        roopFlg = false
        break
      case 'n':
      case 'N':
        await generateEnv(network)
        roopFlg = false
        break
      default:
        message('w', "Invalid choice. Please enter 'y' or 'n'.")
        break
    }
  }
}

/**
 * IBCデプロイ前準備を行う
 * @param rootDir ルートディレクトリ
 * @param network 環境名
 */
async function prepareIbcDeployment(rootDir: string, network: string) {
  const srcDir = path.join(rootDir, 'deployments', network)
  const destDir = path.join(rootDir, 's3-restore', 'deployments', network)

  // ディレクトリ作成
  await fs.promises.mkdir(destDir, { recursive: true })

  const files = [
    'IBCToken.json',
    'Validator.json',
    'Account.json',
    'AccessCtrl.json',
    'BusinessZoneAccount.json',
    'Provider.json',
  ]

  for (const file of files) {
    const src = path.join(srcDir, file)
    const dest = path.join(destDir, file)
    await fs.promises.copyFile(src, dest)
    console.log(`Copied: ${src} -> ${dest}`)
  }
}
